'use client'

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Trash2, Edit } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Checkbox } from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { TagCreationDialog } from '@/components/tag-creation-dialog';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Tag type
type Tag = {
  id: string;
  name: string;
  description?: string;
};

// Topic type
type Topic = {
  id: string;
  value: string;
  weight: number;
  type: string; // Should always be "topic"
  tags: Tag[];
};

// Type for bulk upload topics
type BulkTopic = {
  value: string;
  weight: number;
  tagNames?: string[];
};

export default function TopicsPage() {
  const [topics, setTopics] = useState<Topic[]>([]);
  const [newTopicValue, setNewTopicValue] = useState('');
  const [newTopicWeight, setNewTopicWeight] = useState(10);
  const [editingTopic, setEditingTopic] = useState<Topic | null>(null);
  const [allTags, setAllTags] = useState<Tag[]>([]);
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([]);
  const [bulkJsonInput, setBulkJsonInput] = useState('');
  const { toast } = useToast();

  const fetchTopics = async () => {
    try {
      const response = await fetch('/api/custom-settings?type=topic');
      if (!response.ok) {
        throw new Error('Failed to fetch topics');
      }
      const data: Topic[] = await response.json();
      setTopics(data);
    } catch (error) {
      console.error("Error fetching topics:", error);
      toast({
        title: "Error",
        description: "Failed to fetch topics.",
        variant: "destructive",
      });
    }
  };

  const fetchTags = async () => {
    try {
      const response = await fetch('/api/custom-settings/tags');
      if (!response.ok) {
        throw new Error('Failed to fetch tags');
      }
      const data: Tag[] = await response.json();
      setAllTags(data);
    } catch (error) {
      console.error("Error fetching tags:", error);
      toast({
        title: "Error",
        description: "Failed to fetch tags.",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    fetchTopics();
    fetchTags();
  }, []);

  const handleCreateOrUpdateTopic = async () => {
    if (!newTopicValue.trim()) {
      toast({
        title: "Validation Error",
        description: "Topic value cannot be empty.",
        variant: "destructive",
      });
      return;
    }

    try {
      const method = 'POST';
      const body = editingTopic
        ? JSON.stringify({ id: editingTopic.id, type: 'topic', value: newTopicValue, weight: newTopicWeight, tagIds: selectedTagIds })
        : JSON.stringify({ type: 'topic', value: newTopicValue, weight: newTopicWeight, tagIds: selectedTagIds });

      const response = await fetch('/api/custom-settings', {
        method: method,
        headers: { 'Content-Type': 'application/json' },
        body: body,
      });

      if (!response.ok) {
        throw new Error('Failed to save topic');
      }

      toast({
        title: "Success",
        description: `Topic ${editingTopic ? 'updated' : 'created'} successfully.`,
      });
      setNewTopicValue('');
      setNewTopicWeight(10);
      setEditingTopic(null);
      setSelectedTagIds([]);
      fetchTopics();
    } catch (error) {
      console.error("Error saving topic:", error);
      toast({
        title: "Error",
        description: `Failed to save topic: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
    }
  };

  const handleBulkUploadTopics = async () => {
    if (!bulkJsonInput.trim()) {
      toast({
        title: "Validation Error",
        description: "Bulk upload JSON cannot be empty.",
        variant: "destructive",
      });
      return;
    }

    try {
      const topicsToUpload: BulkTopic[] = JSON.parse(bulkJsonInput);

      if (!Array.isArray(topicsToUpload)) {
        throw new Error("Input is not a valid JSON array of topics.");
      }

      const uploadPromises = topicsToUpload.map(async (topic) => {
        if (!topic.value || typeof topic.value !== 'string' || !topic.weight || typeof topic.weight !== 'number') {
          throw new Error("Each topic must have a 'value' (string) and 'weight' (number).");
        }
        const topicBody = { ...topic, type: 'topic' };

        const response = await fetch('/api/custom-settings', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(topicBody),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(`Failed to upload topic '${topic.value}': ${errorData.message || response.statusText}`);
        }
        return response.json();
      });

      await Promise.all(uploadPromises);

      toast({
        title: "Success",
        description: "All topics uploaded successfully.",
      });
      setBulkJsonInput('');
      fetchTopics();
    } catch (error) {
      console.error("Error bulk uploading topics:", error);
      toast({
        title: "Error",
        description: `Failed to bulk upload topics: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
    }
  };

  const handleDeleteTopic = async (id: string) => {
    try {
      const response = await fetch(`/api/custom-settings/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete topic');
      }

      toast({
        title: "Success",
        description: "Topic deleted successfully.",
      });
      fetchTopics();
    } catch (error) {
      console.error("Error deleting topic:", error);
      toast({
        title: "Error",
        description: `Failed to delete topic: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
    }
  };

  const startEditing = (topic: Topic) => {
    setEditingTopic(topic);
    setNewTopicValue(topic.value);
    setNewTopicWeight(topic.weight);
    setSelectedTagIds(topic.tags.map(tag => tag.id));
  };

  const cancelEditing = () => {
    setEditingTopic(null);
    setNewTopicValue('');
    setNewTopicWeight(10);
    setSelectedTagIds([]);
  };

  return (
    <div className="container mx-auto p-6">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>{editingTopic ? 'Edit Topic' : 'Add New Topic'}</CardTitle>
          <CardDescription>
            {editingTopic ? 'Modify the selected topic and its weight and tags.' : 'Create a new topic with a specific weight and tags.'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <Label htmlFor="topicValue">Topic Name</Label>
              <Input
                id="topicValue"
                value={newTopicValue}
                onChange={(e) => setNewTopicValue(e.target.value)}
                placeholder="e.g., Travel, Technology"
              />
            </div>
            <div>
              <Label htmlFor="topicWeight">Weight</Label>
              <Input
                id="topicWeight"
                type="number"
                value={newTopicWeight}
                onChange={(e) => setNewTopicWeight(parseInt(e.target.value) || 0)}
                min="0"
                placeholder="e.g., 10"
              />
            </div>
            <div>
              <Label htmlFor="topicTags">Tags</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-between">
                    Select Tags ({selectedTagIds.length})
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[200px] p-0">
                  <div className="flex flex-col p-1">
                    {allTags.map((tag) => (
                      <div
                        key={tag.id}
                        className="flex items-center space-x-2 py-1.5 px-2 rounded-sm cursor-pointer hover:bg-accent hover:text-accent-foreground"
                        onClick={() => {
                          setSelectedTagIds(prev =>
                            prev.includes(tag.id)
                              ? prev.filter(id => id !== tag.id)
                              : [...prev, tag.id]
                          );
                        }}
                      >
                        <Checkbox
                          id={`tag-${tag.id}`}
                          checked={selectedTagIds.includes(tag.id)}
                        />
                        <Label htmlFor={`tag-${tag.id}`}>{tag.name}</Label>
                      </div>
                    ))}
                    {/* Option to add new tag */}
                    <div className="h-px bg-border my-1" />
                    <div className="p-2">
                      <TagCreationDialog
                        onTagCreated={(createdTag) => {
                          setAllTags(prev => [...prev, createdTag]);
                          setSelectedTagIds(prev => [...prev, createdTag.id]);
                        }}
                      />
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>
          <div className="mt-4 flex gap-2">
            <Button onClick={handleCreateOrUpdateTopic}>
              {editingTopic ? 'Update Topic' : 'Add Topic'}
            </Button>
            {editingTopic && (
              <Button variant="outline" onClick={cancelEditing}>
                Cancel
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Bulk Upload Topics</CardTitle>
          <CardDescription>
            Upload multiple topics at once using a JSON array. Each object should have 'value' (string), 'weight' (number), and optionally 'tagNames' (array of strings).
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            <div>
              <Label htmlFor="bulkJson">JSON Input</Label>
              <textarea
                id="bulkJson"
                value={bulkJsonInput}
                onChange={(e) => setBulkJsonInput(e.target.value)}
                placeholder={`[
  { "value": "Travel", "weight": 10, "tagNames": ["leisure", "vacation"] },
  { "value": "Technology", "weight": 5, "tagNames": ["modern", "digital"] }
]`}
                rows={8}
                className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
            <Button onClick={handleBulkUploadTopics}>
              Upload Topics
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Existing Topics</CardTitle>
          <CardDescription>Manage your custom topics, their weights, and associated tags.</CardDescription>
        </CardHeader>
        <CardContent>
          {topics.length === 0 ? (
            <p className="text-center text-muted-foreground">No custom topics found.</p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Topic Name</TableHead>
                  <TableHead>Weight</TableHead>
                  <TableHead>Tags</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {topics.map((topic) => (
                  <TableRow key={topic.id}>
                    <TableCell className="font-medium">{topic.value}</TableCell>
                    <TableCell>{topic.weight}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {topic.tags.map(tag => (
                          <TooltipProvider key={tag.id}>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Badge variant="secondary">{tag.name}</Badge>
                              </TooltipTrigger>
                              {tag.description && (
                                <TooltipContent>
                                  <p>{tag.description}</p>
                                </TooltipContent>
                              )}
                            </Tooltip>
                          </TooltipProvider>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => startEditing(topic)}
                        className="mr-2"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteTopic(topic.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

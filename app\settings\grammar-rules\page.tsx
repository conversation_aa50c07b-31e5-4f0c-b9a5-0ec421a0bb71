'use client'

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Trash2, Edit } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Checkbox } from '@/components/ui/checkbox'; // New import
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'; // Already imported, but good to list
import { Badge } from '@/components/ui/badge'; // New import
import { cn } from '@/lib/utils'; // New import for cn
import { TagCreationDialog } from '@/components/tag-creation-dialog';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// New Tag type
type Tag = {
  id: string;
  name: string;
  description?: string;
};

// Updated GrammarRule type
type GrammarRule = {
  id: string;
  value: string;
  weight: number;
  type: string; // Should always be "grammarRule"
  tags: Tag[]; // New field for tags
};

// New type for bulk upload rules
type BulkGrammarRule = {
  value: string;
  weight: number;
  tagNames?: string[];
};

export default function GrammarRulesPage() {
  const [rules, setRules] = useState<GrammarRule[]>([]);
  const [newRuleValue, setNewRuleValue] = useState('');
  const [newRuleWeight, setNewRuleWeight] = useState(10);
  const [editingRule, setEditingRule] = useState<GrammarRule | null>(null);
  const [allTags, setAllTags] = useState<Tag[]>([]); // New state for all tags
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([]); // New state for selected tags
  const [bulkJsonInput, setBulkJsonInput] = useState(''); // New state for bulk JSON input
  const { toast } = useToast();

  const fetchRules = async () => {
    try {
      const response = await fetch('/api/custom-settings?type=grammarRule');
      if (!response.ok) {
        throw new Error('Failed to fetch grammar rules');
      }
      const data: GrammarRule[] = await response.json();
      setRules(data);
    } catch (error) {
      console.error("Error fetching rules:", error);
      toast({
        title: "Error",
        description: "Failed to fetch grammar rules.",
        variant: "destructive",
      });
    }
  };

  const fetchTags = async () => {
    try {
      const response = await fetch('/api/custom-settings/tags');
      if (!response.ok) {
        throw new Error('Failed to fetch tags');
      }
      const data: Tag[] = await response.json();
      setAllTags(data);
    } catch (error) {
      console.error("Error fetching tags:", error);
      toast({
        title: "Error",
        description: "Failed to fetch tags.",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    fetchRules();
    fetchTags(); // Fetch tags on component mount
  }, []);

  const handleCreateOrUpdateRule = async () => {
    if (!newRuleValue.trim()) {
      toast({
        title: "Validation Error",
        description: "Grammar rule value cannot be empty.",
        variant: "destructive",
      });
      return;
    }

    try {
      const method = 'POST'; // POST handles both create and update
      const body = editingRule
        ? JSON.stringify({ id: editingRule.id, type: 'grammarRule', value: newRuleValue, weight: newRuleWeight, tagIds: selectedTagIds })
        : JSON.stringify({ type: 'grammarRule', value: newRuleValue, weight: newRuleWeight, tagIds: selectedTagIds });

      const response = await fetch('/api/custom-settings', {
        method: method,
        headers: { 'Content-Type': 'application/json' },
        body: body,
      });

      if (!response.ok) {
        throw new Error('Failed to save grammar rule');
      }

      toast({
        title: "Success",
        description: `Grammar rule ${editingRule ? 'updated' : 'created'} successfully.`,
      });
      setNewRuleValue('');
      setNewRuleWeight(10);
      setEditingRule(null);
      setSelectedTagIds([]); // Clear selected tags
      fetchRules(); // Re-fetch to update the list
    } catch (error) {
      console.error("Error saving rule:", error);
      toast({
        title: "Error",
        description: `Failed to save grammar rule: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
    }
  };

  const handleBulkUploadRules = async () => {
    if (!bulkJsonInput.trim()) {
      toast({
        title: "Validation Error",
        description: "Bulk upload JSON cannot be empty.",
        variant: "destructive",
      });
      return;
    }

    try {
      const rulesToUpload: BulkGrammarRule[] = JSON.parse(bulkJsonInput);

      if (!Array.isArray(rulesToUpload)) {
        throw new Error("Input is not a valid JSON array of rules.");
      }

      const uploadPromises = rulesToUpload.map(async (rule) => {
        if (!rule.value || typeof rule.value !== 'string' || !rule.weight || typeof rule.weight !== 'number') {
          throw new Error("Each rule must have a 'value' (string) and 'weight' (number).");
        }
        // Ensure type is always 'grammarRule'
        const ruleBody = { ...rule, type: 'grammarRule' };

        const response = await fetch('/api/custom-settings', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(ruleBody),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(`Failed to upload rule '${rule.value}': ${errorData.message || response.statusText}`);
        }
        return response.json();
      });

      await Promise.all(uploadPromises);

      toast({
        title: "Success",
        description: "All grammar rules uploaded successfully.",
      });
      setBulkJsonInput('');
      fetchRules(); // Re-fetch to update the list
    } catch (error) {
      console.error("Error bulk uploading rules:", error);
      toast({
        title: "Error",
        description: `Failed to bulk upload grammar rules: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
    }
  };

  const handleDeleteRule = async (id: string) => {
    try {
      const response = await fetch(`/api/custom-settings/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete grammar rule');
      }

      toast({
        title: "Success",
        description: "Grammar rule deleted successfully.",
      });
      fetchRules(); // Re-fetch to update the list
    } catch (error) {
      console.error("Error deleting rule:", error);
      toast({
        title: "Error",
        description: `Failed to delete grammar rule: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
    }
  };

  const startEditing = (rule: GrammarRule) => {
    setEditingRule(rule);
    setNewRuleValue(rule.value);
    setNewRuleWeight(rule.weight);
    setSelectedTagIds(rule.tags.map(tag => tag.id)); // Populate selected tags
  };

  const cancelEditing = () => {
    setEditingRule(null);
    setNewRuleValue('');
    setNewRuleWeight(10);
    setSelectedTagIds([]); // Clear selected tags
  };

  return (
    <div className="container mx-auto p-6">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>{editingRule ? 'Edit Grammar Rule' : 'Add New Grammar Rule'}</CardTitle>
          <CardDescription>
            {editingRule ? 'Modify the selected grammar rule and its weight and tags.' : 'Create a new grammar rule with a specific weight and tags.'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <Label htmlFor="ruleValue">Rule Name</Label>
              <Input
                id="ruleValue"
                value={newRuleValue}
                onChange={(e) => setNewRuleValue(e.target.value)}
                placeholder="e.g., Present Perfect"
              />
            </div>
            <div>
              <Label htmlFor="ruleWeight">Weight</Label>
              <Input
                id="ruleWeight"
                type="number"
                value={newRuleWeight}
                onChange={(e) => setNewRuleWeight(parseInt(e.target.value) || 0)}
                min="0"
                placeholder="e.g., 10"
              />
            </div>
            <div>
              <Label htmlFor="ruleTags">Tags</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-between">
                    Select Tags ({selectedTagIds.length})
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[200px] p-0">
                  <div className="flex flex-col p-1">
                    {allTags.map((tag) => (
                      <div
                        key={tag.id}
                        className="flex items-center space-x-2 py-1.5 px-2 rounded-sm cursor-pointer hover:bg-accent hover:text-accent-foreground"
                        onClick={() => {
                          setSelectedTagIds(prev =>
                            prev.includes(tag.id)
                              ? prev.filter(id => id !== tag.id)
                              : [...prev, tag.id]
                          );
                        }}
                      >
                        <Checkbox
                          id={`tag-${tag.id}`}
                          checked={selectedTagIds.includes(tag.id)}
                        />
                        <Label htmlFor={`tag-${tag.id}`}>{tag.name}</Label>
                      </div>
                    ))}
                    {/* Option to add new tag */}
                    <div className="h-px bg-border my-1" />
                    <div className="p-2">
                      <TagCreationDialog
                        onTagCreated={(createdTag) => {
                          setAllTags(prev => [...prev, createdTag]);
                          setSelectedTagIds(prev => [...prev, createdTag.id]);
                        }}
                      />
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>
          <div className="mt-4 flex gap-2">
            <Button onClick={handleCreateOrUpdateRule}>
              {editingRule ? 'Update Rule' : 'Add Rule'}
            </Button>
            {editingRule && (
              <Button variant="outline" onClick={cancelEditing}>
                Cancel
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Bulk Upload Grammar Rules</CardTitle>
          <CardDescription>
            Upload multiple grammar rules at once using a JSON array. Each object should have 'value' (string), 'weight' (number), and optionally 'tagNames' (array of strings).
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            <div>
              <Label htmlFor="bulkJson">JSON Input</Label>
              <textarea
                id="bulkJson"
                value={bulkJsonInput}
                onChange={(e) => setBulkJsonInput(e.target.value)}
                placeholder={`[
  { "value": "Rule 1", "weight": 10, "tagNames": ["tagA", "tagB"] },
  { "value": "Rule 2", "weight": 5, "tagNames": ["tagC"] }
]`}
                rows={8}
                className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
            <Button onClick={handleBulkUploadRules}>
              Upload Rules
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Existing Grammar Rules</CardTitle>
          <CardDescription>Manage your custom grammar rules, their weights, and associated tags.</CardDescription>
        </CardHeader>
        <CardContent>
          {rules.length === 0 ? (
            <p className="text-center text-muted-foreground">No custom grammar rules found.</p>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Rule Name</TableHead>
                  <TableHead>Weight</TableHead>
                  <TableHead>Tags</TableHead> {/* New TableHead */}
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {rules.map((rule) => (
                  <TableRow key={rule.id}>
                    <TableCell className="font-medium">{rule.value}</TableCell>
                    <TableCell>{rule.weight}</TableCell>
                    <TableCell> {/* New TableCell for tags */}
                      <div className="flex flex-wrap gap-1">
                        {rule.tags.map(tag => (
                          <TooltipProvider key={tag.id}>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Badge variant="secondary">{tag.name}</Badge>
                              </TooltipTrigger>
                              {tag.description && (
                                <TooltipContent>
                                  <p>{tag.description}</p>
                                </TooltipContent>
                              )}
                            </Tooltip>
                          </TooltipProvider>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => startEditing(rule)}
                        className="mr-2"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteRule(rule.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}